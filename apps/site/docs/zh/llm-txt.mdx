# LLMs.txt 文档

如何让 Cursor、Windstatic、GitHub Copilot、ChatGPT 和 Claude 等工具理解 Midscene.js。

我们支持 LLMs.txt 文件，使 Midscene.js 的文档可供大型语言模型使用。

## 目录概览

以下文件可供使用：

- [llms.txt](https://midscenejs.com/llms.txt)：主要的 LLMs.txt 文件
- [llms-full.txt](https://midscenejs.com/llms-full.txt)：Midscene.js 的完整文档


## 使用方法

### Cursor

在 Cursor 中使用 `@Docs` 功能来将 LLMs.txt 文件包含到你的项目中。

[阅读更多](https://docs.cursor.com/context/@-symbols/@-docs)

### Windstatic

使用 `@` 或在你的 `.windsurfrules` 文件中引用 LLMs.txt 文件。

[阅读更多](https://docs.windsurf.com/windsurf/getting-started#memories-and-rules)


