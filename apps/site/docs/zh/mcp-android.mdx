import SetupEnv from './common/setup-env.mdx';

# MCP 服务

Midscene 提供了专门的 MCP 服务，允许 AI 助手通过自然语言命令控制 Android 设备，自动化执行移动应用测试任务。

:::info 什么是 MCP
[MCP](https://modelcontextprotocol.io/introduction) 是一种标准化的方式，使 AI 模型能够与外部工具和功能进行交互。MCP 服务器暴露一组工具后，AI 模型可以调用这些工具来执行各种任务。对于 Midscene 来说，这些工具允许 AI 模型连接 Android 设备、启动应用、与 UI 元素交互等等。
:::

## 使用场景

- 在 Android 设备上执行自动化测试
- 控制 Android 应用进行 UI 交互

## 设置 Midscene MCP

### 前提条件

1. OpenAI API 密钥或其他支持的 AI 模型提供商，更多信息请查看 [选择 AI 模型](./choose-a-model)。
2. [Android adb](https://developer.android.com/tools/adb?hl=zh-cn) 工具已安装并配置
3. Android 设备已启用 USB 调试模式并连接到电脑

### 配置

将 Midscene MCP 服务器添加到你的 MCP 配置中，注意不要遗漏 `MIDSCENE_MCP_ANDROID_MODE` 环境变量：

```json
{
  "mcpServers": {
    "mcp-midscene": {
      "command": "npx",
      "args": ["-y", "@midscene/mcp"],
      "env": {
        "MIDSCENE_MODEL_NAME": "REPLACE_WITH_YOUR_MODEL_NAME",
        "OPENAI_API_KEY": "REPLACE_WITH_YOUR_OPENAI_API_KEY",
        "MIDSCENE_MCP_ANDROID_MODE": "true",
        "MCP_SERVER_REQUEST_TIMEOUT": "800000"
      }
    }
  }
}
```

其中有关配置 AI 模型的信息，请参阅[选择 AI 模型](./choose-a-model)。

## 可用工具

Midscene MCP 提供以下 Android 设备自动化工具：

| 功能分类       | 工具名称                      | 功能描述                            |
| -------------- | ----------------------------- | ----------------------------------- |
| **设备管理**   | midscene_android_list_devices | 列出所有已连接的 Android 设备       |
|                | midscene_android_connect      | 连接到指定的 Android 设备           |
| **应用控制**   | midscene_android_launch       | 在 Android 设备上启动应用或打开网页 |
| **系统操作**   | midscene_android_back         | 按下 Android 设备的返回键           |
|                | midscene_android_home         | 按下 Android 设备的主页键           |
| **页面交互**   | midscene_aiTap                | 点击通过自然语言描述的元素          |
|                | midscene_aiInput              | 在表单字段或元素中输入文本          |
|                | midscene_aiKeyboardPress      | 按下特定键盘按键                    |
|                | midscene_aiScroll             | 滚动页面或特定元素                  |
| **验证和观察** | midscene_aiWaitFor            | 等待页面上的条件为真                |
|                | midscene_aiAssert             | 断言页面上的条件为真                |
|                | midscene_screenshot           | 对当前页面截图                      |

### 设备管理

- **midscene_android_list_devices**：列出所有已连接的 Android 设备

  ```
  参数：无
  ```

- **midscene_android_connect**：连接到指定的 Android 设备
  ```
  参数：
  - deviceId：（可选）要连接的设备 ID。如果未提供，使用第一个可用设备
  ```

### 应用控制

- **midscene_android_launch**：在 Android 设备上启动应用或打开网页
  ```
  参数：
  - uri：要启动的应用包名、Activity 名称或要打开的网页 URL
  ```

### 系统操作

- **midscene_android_back**：按下 Android 设备的返回键

  ```
  参数：无
  ```

- **midscene_android_home**：按下 Android 设备的主页键
  ```
  参数：无
  ```

### 页面交互

- **midscene_aiTap**：点击通过自然语言描述的元素

  ```
  参数：
  - locate：要点击元素的自然语言描述
  ```

- **midscene_aiInput**：在表单字段或元素中输入文本

  ```
  参数：
  - value：要输入的文本
  - locate：要输入文本的元素的自然语言描述
  ```

- **midscene_aiKeyboardPress**：按下特定键盘按键

  ```
  参数：
  - key：要按下的按键（例如 'Enter'、'Tab'、'Escape'）
  - locate：（可选）在按键前要聚焦的元素描述
  - deepThink：（可选）如果为 true，使用更精确的元素定位
  ```

- **midscene_aiScroll**：滚动页面或特定元素
  ```
  参数：
  - direction：'up'、'down'、'left' 或 'right'
  - scrollType：'once'、'untilBottom'、'untilTop'、'untilLeft' 或 'untilRight'
  - distance：（可选）以像素为单位的滚动距离
  - locate：（可选）要滚动的元素描述
  - deepThink：（可选）如果为 true，使用更精确的元素定位
  ```

### 验证和观察

- **midscene_aiWaitFor**：等待页面上的条件为真

  ```
  参数：
  - assertion：要等待的条件的自然语言描述
  - timeoutMs：（可选）最大等待时间（毫秒）
  - checkIntervalMs：（可选）检查条件的频率
  ```

- **midscene_aiAssert**：断言页面上的条件为真

  ```
  参数：
  - assertion：要检查的条件的自然语言描述
  ```

- **midscene_screenshot**：对当前页面截图
  ```
  参数：
  - name：截图的名称
  ```

## 常见问题

### 如何连接 Android 设备？

1. 确保已安装 Android SDK 并配置 ADB
2. 在 Android 设备上启用开发者选项和 USB 调试
3. 使用 USB 线连接设备到电脑
4. 运行 `adb devices` 确认设备已连接
5. 在 MCP 中使用 `midscene_android_list_devices` 查看可用设备

### 如何启动 Android 应用？

使用 `midscene_android_launch` 工具，参数可以是：

- 应用包名：如 `com.android.chrome`
- Activity 名称：如 `com.android.chrome/.MainActivity`
- 网页 URL：如 `https://www.example.com`

### 本地如果出现多个 Client 会导致 Server port 占用

> 问题描述

当用户在本地多个 Client （Claude Desktop、Cursor MCP、） 中同时使用了 Midscene MCP 将会出现端口占用导致服务报错

> 如何解决

- 将多余的 client 中的 MCP server 暂时先关闭
- 执行命令

```bash
# For macOS/Linux:
lsof -i:3766 | awk 'NR>1 {print $2}' | xargs -r kill -9

# For Windows:
FOR /F "tokens=5" %i IN ('netstat -ano ^| findstr :3766') DO taskkill /F /PID %i
```

### 如何获取 Midscene 执行的报告

在每次执行完任务后都会生成 Midscene 任务报告，可以在命令行直接打开该 html 报告

```bash
# 将打开的地址替换为你的报告文件名
open report_file_name.html
```

![image](https://lf3-static.bytednsdoc.com/obj/eden-cn/ozpmyhn_lm_hymuPild/ljhwZthlaukjlkulzlp/midscene/image.png)
