import SetupEnv from './common/setup-env.mdx';

# MCP 服务

Midscene 提供了 MCP 服务，允许 AI 助手通过自然语言命令控制浏览器，自动化执行 UI 任务，以及生成 Midscene 自动化脚本。

:::info 什么是 MCP
[MCP](https://modelcontextprotocol.io/introduction) 是一种标准化的方式，使 AI 模型能够与外部工具和功能进行交互。MCP 服务器暴露一组工具后，AI 模型可以调用这些工具来执行各种任务。对于 Midscene 来说，这些工具允许 AI 模型控制浏览器、导航网页、与 UI 元素交互等等。
:::

## 使用场景

* 控制浏览器执行自动化任务
* 生成 Midscene 的自动化脚本

### 使用示例

> 给 Sauce Demo 站点生成 Midscene 测试用例

<video src="https://lf3-static.bytednsdoc.com/obj/eden-cn/ozpmyhn_lm_hymuPild/ljhwZthlaukjlkulzlp/midscene/en-midscene-mcp-Sauce-Demo.mp4" controls/>

## 设置 Midscene MCP

### 前提条件

1. OpenAI API 密钥或其他支持的 AI 模型提供商，更多信息请查看 [选择 AI 模型](./choose-a-model)。
2. 对于 Chrome 浏览器集成（桥接模式）：
   - 安装 Midscene Chrome 扩展（从 [Chrome Web Extension](https://chromewebstore.google.com/detail/midscenejs/gbldofcpkknbggpkmbdaefngejllnief?hl=zh-CN&utm_source=ext_sidebar) 下载）
   - 在扩展中切换到"桥接模式"并点击"允许连接"

### 配置

将 Midscene MCP 服务器添加到你的 MCP 配置中：

```json
{
  "mcpServers": {
    "mcp-midscene": {
      "command": "npx",
      "args": ["-y", "@midscene/mcp"],
      "env": {
        "MIDSCENE_MODEL_NAME": "REPLACE_WITH_YOUR_MODEL_NAME",
        "OPENAI_API_KEY": "REPLACE_WITH_YOUR_OPENAI_API_KEY",
        "MCP_SERVER_REQUEST_TIMEOUT": "800000"
      }
    }
  }
}
```

有关配置 AI 模型的更多信息，请参阅[选择 AI 模型](./choose-a-model)。

## 可用工具

Midscene MCP 提供以下浏览器自动化工具：

| 功能分类 | 工具名称 | 功能描述 |
|---------|---------|---------|
| **导航** | midscene_navigate | 在当前标签页导航到指定 URL |
| **标签页管理** | midscene_get_tabs | 获取所有打开的浏览器标签页列表 |
| | midscene_set_active_tab | 通过 ID 切换到特定标签页 |
| **页面交互** | midscene_aiTap | 点击通过自然语言描述的元素 |
| | midscene_aiInput | 在表单字段或元素中输入文本 |
| | midscene_aiHover | 悬停在元素上 |
| | midscene_aiKeyboardPress | 按下特定键盘按键 |
| | midscene_aiScroll | 滚动页面或特定元素 |
| **验证和观察** | midscene_aiWaitFor | 等待页面上的条件为真 |
| | midscene_aiAssert | 断言页面上的条件为真 |
| | midscene_screenshot | 对当前页面截图 |
| **Playwright 代码示例** | midscene_playwright_example | 提供了 Midscene 的 Playwright 代码示例 |

### 导航

- **midscene_navigate**：在当前标签页导航到指定 URL
  ```
  参数：
  - url：要导航到的 URL
  ```

### 标签页管理

- **midscene_get_tabs**：获取所有打开的浏览器标签页列表，包括它们的 ID、标题和 URL
  ```
  参数：无
  ```

- **midscene_set_active_tab**：通过 ID 切换到特定标签页
  ```
  参数：
  - tabId：要激活的标签页 ID
  ```

### 页面交互

- **midscene_aiTap**：点击通过自然语言描述的元素
  ```
  参数：
  - locate：要点击元素的自然语言描述
  ```

- **midscene_aiInput**：在表单字段或元素中输入文本
  ```
  参数：
  - value：要输入的文本
  - locate：要输入文本的元素的自然语言描述
  ```

- **midscene_aiHover**：悬停在元素上
  ```
  参数：
  - locate：要悬停元素的自然语言描述
  ```

- **midscene_aiKeyboardPress**：按下特定键盘按键
  ```
  参数：
  - key：要按下的按键（例如 'Enter'、'Tab'、'Escape'）
  - locate：（可选）在按键前要聚焦的元素描述
  - deepThink：（可选）如果为 true，使用更精确的元素定位
  ```

- **midscene_aiScroll**：滚动页面或特定元素
  ```
  参数：
  - direction：'up'、'down'、'left' 或 'right'
  - scrollType：'once'、'untilBottom'、'untilTop'、'untilLeft' 或 'untilRight'
  - distance：（可选）以像素为单位的滚动距离
  - locate：（可选）要滚动的元素描述
  - deepThink：（可选）如果为 true，使用更精确的元素定位
  ```

### 验证和观察

- **midscene_aiWaitFor**：等待页面上的条件为真
  ```
  参数：
  - assertion：要等待的条件的自然语言描述
  - timeoutMs：（可选）最大等待时间（毫秒）
  - checkIntervalMs：（可选）检查条件的频率
  ```

- **midscene_aiAssert**：断言页面上的条件为真
  ```
  参数：
  - assertion：要检查的条件的自然语言描述
  ```

- **midscene_screenshot**：对当前页面截图
  ```
  参数：
  - name：截图的名称
  ```

## 常见问题

### Midscene MCP 对比其他浏览器 MCP 有什么优势？

- Midscene MCP 默认支持了 Bridge 模式，能够**直接控制你当前正在使用的浏览器**，**无需重新登录或者下载浏览器**
- Midscene MCP 内置了对于浏览器页面控制和操作的最佳 Prompt 模板和操作执行实践，对比其他 MCP 实现，**能够提供更加稳定和可靠的浏览器自动化体验**
- Midscene MCP 执行完成任务后将会自动生成执行用例报告，**随时可观看执行过程**


### 本地如果出现多个 Client 会导致 Server port 占用

> 问题描述

当用户在本地多个 Client （Claude Desktop、Cursor MCP、） 中同时使用了 Midscene MCP 将会出现端口占用导致服务报错

> 如何解决

- 将多余的 client 中的 MCP server 暂时先关闭
- 执行命令

```bash
# For macOS/Linux:
lsof -i:3766 | awk 'NR>1 {print $2}' | xargs -r kill -9

# For Windows:
FOR /F "tokens=5" %i IN ('netstat -ano ^| findstr :3766') DO taskkill /F /PID %i
```

### 如何获取 Midscene 执行的报告

在每次执行完任务后都会生成 Midscene 任务报告，可以在命令行直接打开该 html 报告

```bash
# 将打开的地址替换为你的报告文件名
open report_file_name.html
```

![image](https://lf3-static.bytednsdoc.com/obj/eden-cn/ozpmyhn_lm_hymuPild/ljhwZthlaukjlkulzlp/midscene/image.png)
