import StartExperience from './common/start-experience.mdx';
import PrepareAndroid from './common/prepare-android.mdx';

# Quick Experience with Android

By using Midscene.js playground, you can quickly experience the main features of Midscene on Android devices, without needing to write any code.

![](/android-playground.png)

<PrepareAndroid />

## Run Playground

```bash
npx --yes @midscene/android-playground
```

## Config API Key

Click the gear button to enter the configuration page and paste your API key config.

![](/android-set-env.png)

Refer to [Config Model and Provider](./model-provider) document, config the API Key.

<StartExperience />

* [Integrate javascript SDK with Android](./integrate-with-android)
