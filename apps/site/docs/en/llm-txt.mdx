# LLMs.txt Documentation

How to get tools like Cursor, Windstatic, GitHub Copilot, ChatGPT, and Claude to understand Midscene.js.

We support LLMs.txt files for making the Midscene.js documentation available to large language models.

## Directory Overview

The following files are available.

- [llms.txt](https://midscenejs.com/llms.txt): The main LLMs.txt file
- [llms-full.txt](https://midscenejs.com/llms-full.txt): The complete documentation for Midscene.js


## Usage

### Cursor

Use `@Docs` feature in Cursor to include the LLMs.txt files in your project.

[Read more](https://docs.cursor.com/context/@-symbols/@-docs)

### Windstatic

Reference the LLMs.txt files using `@` or in your `.windsurfrules` files.

[Read more](https://docs.windsurf.com/windsurf/getting-started#memories-and-rules)


