@import './common.less';

.side-bar {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 16px;
  background: #fff;

  .page-nav {
    height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-shrink: 0;

    .page-nav-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .page-nav-toolbar {
        display: flex;
        align-items: center;
        gap: 16px;

        .icon-button {
          cursor: pointer;
          display: flex;
          align-items: center;

          &:hover {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .top-controls {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    min-height: 0;
  }


  .report-overview {
    flex-shrink: 0;
  }

  .task-token-section {
    flex-shrink: 0;
    height: 44px;
    line-height: 44px;
    padding: 0 20px;
    font-size: 12px;
  }

  .task-meta {
    color: @weak-text;
    font-weight: normal;
    padding-left: @side-horizontal-padding;
  }

  .task-meta-tokens {
    display: grid;
    grid-template-columns: 1fr auto 112px 26px;
    gap: 8px;
    align-items: center;
    font-size: 12px;

    .token-total-label {
      grid-column: 1;
      font-weight: 500;
      color: #808080;
    }

    .token-total-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:nth-child(2) {
        grid-column: 3;
      }

      &:nth-child(3) {
        grid-column: 4;
      }

      .token-value {
        font-size: 14px;
        color: #595959;
        font-weight: normal;
        line-height: 1.2;
      }
    }
  }


  .side-seperator {
    border-top: 1px solid transparent;

    &.side-seperator-line {
      margin: 0 12px;
      border-top: 1px solid @border-color;
    }

    &.side-seperator-space-up {
      margin-top: @side-vertical-spacing;
    }

    &.side-seperator-space-down {
      margin-bottom: @side-vertical-spacing;
    }
  }

  .side-sub-title {
    padding: 0 20px;
    margin-bottom: 6px;
    font-weight: 500;
    color: #1A1A1A;
    flex-shrink: 0;
  }

  .task-list {}

  .side-item {
    cursor: pointer;
    transition: .1s;
    padding: 0 20 0 22px;

    &:hover {
      background: @hover-bg;
    }

    &.selected {
      background: @selected-bg;
    }

    .side-item-content {
      padding: 0 @side-horizontal-padding 0 calc(@side-horizontal-padding + 10px);
    }
  }

  .side-item-name {
    padding: 0 0 0 calc(@side-horizontal-padding + 10px);
    position: relative;
    display: grid;
    gap: 8px;
    align-items: center;
    height: 32px;
    line-height: 32px;
    color: #595959;


    grid-template-columns: 1fr auto;


    &.pro-mode {
      grid-template-columns: 1fr auto 55px 65px;
    }

    .status-icon {
      position: absolute;
      left: 0;
      display: inline-block;
      color: #AAA;
      font-size: 12px;
      line-height: 10px;
      top: 50%;
      margin-top: -5px;
    }

    .title {
      grid-column: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }

    .status-text {
      grid-column: 2;
      font-size: 14px;
      text-align: right;
    }

    .usage-column {
      text-align: right;

      &.prompt-tokens {
        grid-column: 3;
      }

      &.completion-tokens {
        grid-column: 4;
      }
    }
  }

  .table-header {
    padding: 0 20px 0 20px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 8px;
    align-items: center;
    height: 28px;
    margin-bottom: 4px;
    font-size: 12px;
    color: #808080;
    font-weight: 500;
    flex-shrink: 0;

    &.pro-mode {
      grid-template-columns: 1fr auto 55px 65px;
    }

    .header-name {
      grid-column: 1;
      text-align: left;
    }

    .header-time {
      grid-column: 2;
      text-align: right;
    }

    .header-prompt {
      grid-column: 3;
      text-align: right;
    }

    .header-completion {
      grid-column: 4;
      text-align: right;
    }
  }

  .execution-info-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    min-height: 0;

    .execution-info-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      height: 32px;
      line-height: 32px;
      padding-left: 20px;
      padding-right: 20px;
      flex-shrink: 0;

      .execution-info-title-left {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .execution-info-title-right {
        display: flex;
        align-items: center;

        .token-usage-checkbox {
          .ant-checkbox-inner {
            width: 14px;
            height: 14px;
            background-image: url('../icons/checkbox-unchecked.svg');
            background-size: contain;
            border: none;
          }

          &.ant-checkbox-checked .ant-checkbox-inner {
            background-image: url('../icons/checkbox-checked.svg');
          }
        }

        span {
          color: #595959;
          font-size: 12px;
          padding: 0 2px;
        }
      }
    }

    .executions-wrapper {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      min-height: 0;
    }
  }

}