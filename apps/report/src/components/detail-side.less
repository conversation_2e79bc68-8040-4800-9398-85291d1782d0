@import './common.less';

@subSpacing: 5px;
@lightTextColor: #777;

.detail-side {
  height: 100%;
  overflow-y: auto;

  .info-tabs {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    border-bottom: 1px solid #f0f0f0;

    .info-tab {
      display: inline-block;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      position: relative;

      &.active {
        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #1677ff;
        }
      }
    }
  }

  .detail-side-timeline {
    margin-left: 20px;
  }

  details {
    summary {
      height: 40px;
      line-height: 40px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: 500;
      list-style: none;
      display: flex;
      align-items: center;
      padding: 0 20px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);

      &::-webkit-details-marker {
        display: none;
      }

      &::before {
        content: '';
        background-image: url('../icons/arrow.svg');
        background-size: contain;
        width: 10px;
        height: 6px;
        display: inline-block;
        margin-right: 8px;
        transition: transform 0.2s;
        transform: rotate(-90deg);
      }
    }
  }

  details:first-of-type summary {
    border-top: none;
  }

  details[open]>summary {
    &::before {
      transform: rotate(0deg);
      position: relative;
    }
  }

  h2 {
    padding-top: 0;
    margin-top: 0;
    margin-bottom: 4px;
  }

  .ant-tag {
    margin-top: 2px;
  }

  .log-content {
    padding: 20px;
  }

  .meta-kv {
    word-wrap: break-word;
    padding-bottom: 5px;

    .meta {
      box-sizing: border-box;
      padding: 6px 20px;
      width: 100%;
      display: flex;
      flex-direction: column;
      line-height: 22px;

      .meta-key {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }

      .meta-value {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  .item-list {
    cursor: default;
    margin-bottom: 10px;

    .item {
      transition: .1s;
      border-radius: @subSpacing;
      margin-bottom: @side-vertical-spacing;
      position: relative;

      &.item-lite {
        border: none;
        padding: 0;
      }
    }

    .item-highlight {
      color: #FFF;

      .subtitle {
        color: #CCC;
      }
    }

    .item-extra {
      position: absolute;
      right: @side-horizontal-padding;
      top: @side-horizontal-padding;
      color: @lightTextColor;
    }

    .title-right-padding {
      padding-right: 15px;
    }

    .title {
      color: #00000073;
      font-size: 14px;
      display: block;
      padding-left: 20px;

      .title-tag {
        display: inline-block;
        margin-left: 6px;
        color: @lightTextColor;
        font-size: 14px;
        line-height: 18px;
      }
    }

    .subtitle {
      padding-left: 20px;
      font-weight: normal;
      font-size: 14px;
      color: #00000073;
    }

    .description {
      margin-top: @subSpacing;
      padding: 0 20px;
    }

    .description-content {
      font-size: 14px;
      margin-top: @side-horizontal-padding;
      white-space: break-spaces;
      word-wrap: break-word;
      margin: 0;
    }

    .element-button:hover {
      // font-weight: bold;
      color: #fff;
      background: @main-orange;
    }

    .section-button:hover {
      color: #fff;
      background: #01204E;
    }
  }

  pre {
    text-wrap: balance;
  }
}