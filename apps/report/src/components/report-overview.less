@import './common.less';

.report-overview {
  background: #fff;
  flex-shrink: 0;

  .test-case-stats {
    display: flex;
    gap: 8px;
    padding: 0 12px;

    .stats-card {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      background: #F5F7FA;
      border-radius: 8px;
      flex: 1;
      min-width: 0;
      padding-left: 12px;
      padding-top: 8px;
      padding-bottom: 8px;

      .stats-label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 500;
      }

      .stats-value {
        font-size: 22px;
        font-weight: 700;
        line-height: 26px;
        color: rgba(0, 0, 0, 0.65);

        &.stats-passed {
          color: #00AD4B;
        }

        &.stats-failed {
          color: #E40303;
        }
      }
    }
  }
}