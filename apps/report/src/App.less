@import './components/common.less';

html,
body {
  padding: 0;
  margin: 0;
  line-height: 1;
  overflow: hidden;
  background: #F2F4F7;
}

.rspress-nav {
  transition: .2s;
}

:root {
  --modern-sidebar-width: 0 !important;
  --modern-aside-width: 0 !important;
  --modern-preview-padding: 0 !important;
}

.modern-doc-layout,
.modern-doc {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh;
}

.modern-sidebar,
header.w-full {
  display: none !important;
}

.modern-doc-container {
  padding: 0 !important;
}

footer.mt-8 {
  display: none;
}

// ----------

.page-container {

  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    margin: 0;
  }

  display: flex;
  flex-direction: column;
  height: 100%;
  color: @main-text;
  font-family: -apple-system,
  BlinkMacSystemFont,
  "Segoe UI",
  "Noto Sans",
  Helvetica,
  Arial,
  sans-serif,
  "Apple Color Emoji",
  "Segoe UI Emoji";
  font-size: 14px;
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
  font-synthesis: style;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
  padding: 0 8px;
}

.page-side {
  height: calc(100% - 8px);
  background: #F2F4F7;
  padding-right: 8px;
  margin-bottom: 8px;
}

.page-nav {
  height: 48px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;

  .page-nav-left {
    display: flex;
    flex-direction: row;

    .page-nav-title {
      padding-left: 4px;
      font-size: 16px;
      font-weight: 600;

      .page-nav-title-hint {
        margin-left: 9px;
        font-weight: normal;
        color: #808080;
        font-size: 12px;
      }
    }
  }

  .page-nav-right {
    color: #595959;
    font-size: 12px;
  }
}

.cost-str {
  color: @weak-text;
}

.ant-layout {
  flex-grow: 1;
  height: 100%;
}

.main-right {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 8px);
  box-sizing: border-box;
  border-radius: 16px;
  background: #fff;
  margin-bottom: 8px;

  .main-right-header {
    height: 50px;
    line-height: 50px;
    padding-left: 17px;
    font-size: 16px;
    font-weight: 600;
  }

  .replay-all-mode-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    margin: 0 auto;

    .player-tools-wrapper {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      border-left: 1px solid rgba(0, 0, 0, 0.06);
      border-right: 1px solid rgba(0, 0, 0, 0.06);
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
    }
  }

  .main-content {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    overflow: hidden;
    background: #FFF;
    border-radius: 16px;
  }

  &.uploader-wrapper {
    box-sizing: border-box;
    margin: auto;
    max-width: 800px;
    flex-direction: column;
    justify-content: center;

    .uploader {
      width: 100%;
    }

    .demo-loader {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }
  }

  .main-content-container {
    flex-grow: 1;
    height: 100%;
    background: #ffffff;
    overflow: hidden;
    border-right: 1px solid rgba(0, 0, 0, 0.06);
  }

  .main-side {
    box-sizing: border-box;
    overflow-y: scroll;
    height: 100%;
  }

  .json-content {
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}