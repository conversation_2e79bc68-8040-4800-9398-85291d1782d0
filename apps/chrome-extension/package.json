{"name": "chrome-extension", "private": true, "version": "0.12.4", "type": "module", "scripts": {"build": "rsbuild build && npm run pack-extension", "dev": "rsbuild dev --open", "preview": "rsbuild preview", "pack-extension": "node scripts/pack-extension.js"}, "dependencies": {"@ant-design/icons": "^5.3.1", "@midscene/core": "workspace:*", "@midscene/recorder": "workspace:*", "@midscene/report": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/visualizer": "workspace:*", "@midscene/web": "workspace:*", "@types/file-saver": "2.0.7", "antd": "^5.21.6", "canvas-confetti": "1.9.3", "dayjs": "^1.11.11", "file-saver": "2.0.5", "highlight.js": "11.11.1", "jszip": "3.10.1", "react": "18.3.1", "react-dom": "18.3.1", "react-highlight": "0.15.0", "zustand": "4.5.2"}, "devDependencies": {"@rsbuild/core": "^1.3.22", "@rsbuild/plugin-less": "^1.2.4", "@rsbuild/plugin-node-polyfill": "1.3.0", "@rsbuild/plugin-react": "^1.3.1", "@rsbuild/plugin-svgr": "^1.1.1", "@rsbuild/plugin-type-check": "1.2.3", "@tailwindcss/postcss": "4.1.11", "@types/chrome": "0.0.279", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "archiver": "^6.0.0", "less": "^4.2.0", "tailwindcss": "4.1.11", "typescript": "^5.8.3"}}