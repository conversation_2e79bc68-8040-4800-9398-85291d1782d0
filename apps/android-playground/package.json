{"name": "android-playground", "private": true, "version": "0.12.4", "type": "module", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "preview": "rsbuild preview"}, "dependencies": {"@ant-design/icons": "^5.3.1", "@midscene/android": "workspace:*", "@midscene/core": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/visualizer": "workspace:*", "@midscene/web": "workspace:*", "@yume-chan/scrcpy": "^1.1.0", "@yume-chan/scrcpy-decoder-webcodecs": "1.1.0", "antd": "^5.21.6", "dayjs": "^1.11.11", "react": "18.3.1", "react-dom": "18.3.1", "socket.io-client": "4.8.1"}, "devDependencies": {"@rsbuild/core": "^1.3.22", "@rsbuild/plugin-less": "^1.2.4", "@rsbuild/plugin-node-polyfill": "1.3.0", "@rsbuild/plugin-react": "^1.3.1", "@rsbuild/plugin-svgr": "^1.1.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "archiver": "^6.0.0", "less": "^4.2.0", "typescript": "^5.8.3"}}