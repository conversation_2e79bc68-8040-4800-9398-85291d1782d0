body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans',
    Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
  font-size: 14px;
}

.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.app-content {
  height: 100vh;
  overflow: hidden;
}

.app-grid-layout {
  height: 100%;
  display: flex;

  .ant-row {
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
  }
}

.app-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;
  overflow: hidden;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }

  &.left-panel {
    width: 480px;
    flex: none;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  &.right-panel {
    border-radius: 0;
    flex: 1;
    overflow: hidden;
    box-shadow: -4px 0px 20px 0px #0000000A;
  }
}

.panel-content {
  padding: 12px 24px 24px 24px;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(0, 0, 0, 0.08);

  &.left-panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  &.right-panel-content {
    border-radius: 0;
  }

  h2 {
    color: #000;
    font-size: 18px;
    margin-top: 16px;
    margin-bottom: 12px;
  }

  canvas {
    max-width: 100%;
    margin-top: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
  }
}

.command-form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .form-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 24px;
  }

  .command-input-wrapper {
    margin-top: 8px;
  }
}

.result-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
  height: 100%;
}

@media (max-width: 768px) {
  .app-container {
    height: auto;
    min-height: 100vh;
  }

  .app-grid-layout .ant-row {
    flex-wrap: wrap !important;
  }

  .app-panel {
    margin-bottom: 16px;
    height: auto;
    min-height: 200px;
    width: 100% !important;
    flex: 0 0 100% !important;

    &:first-child {
      border-radius: 20px;

      .panel-content {
        border-radius: 20px;
      }
    }
  }

  .panel-content {
    padding: 12px;

    h2 {
      font-size: 16px;
      margin-bottom: 12px;
      padding-bottom: 6px;
    }

    textarea {
      min-height: 100px;
    }
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .app-panel {
    margin-bottom: 16px;
    min-height: 300px;
  }
}

.resize-handle {
  width: 2px;
  background-color: #f0f0f0;
  transition: background-color 0.2s;

  &:hover {
    background-color: #1677ff;
  }
}