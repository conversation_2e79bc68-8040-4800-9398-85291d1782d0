{"editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "cSpell.words": ["AITEST", "<PERSON><PERSON>", "aweme", "bbox", "bytedance", "deepseek", "do<PERSON>o", "do<PERSON><PERSON>", "fkill", "httpbin", "iconfont", "jsonrepair", "modelcontextprotocol", "openrouter", "qwen", "<PERSON><PERSON><PERSON>", "targetcreated", "Volcengine", "xpaths", "<PERSON><PERSON><PERSON>"], "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[plaintext]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "nxConsole.generateAiAgentRules": true}