name: AI unit test
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to checkout'
        required: false
        default: 'main'
        type: string

permissions:
  contents: read

jobs:
  main:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      OPENAI_API_KEY: ${{ secrets.DOUBAO_TOKEN }}
      OPENAI_BASE_URL: ${{ secrets.DOUBAO_BASE_URL }}
      MIDSCENE_MODEL_NAME: 'doubao-1-5-thinking-vision-pro-250428'
      MIDSCENE_USE_DOUBAO_VISION: 1
      CI: 1

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ref: ${{ github.event.inputs.branch || 'main' }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Install puppeteer dependencies
      run: |
        cd packages/web-integration
        npx puppeteer browsers install chrome
    
    - name: Run tests
      run: pnpm run test:ai
      id: test-ai
      continue-on-error: true

    - name: Upload test-ai output
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-ai-output
        path: ${{ github.workspace }}/packages/web-integration/midscene_run/report
        if-no-files-found: ignore

    - name: Check if script failed
      if: steps.test-ai.outcome == 'failure'
      run: exit 1
