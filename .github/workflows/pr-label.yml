name: PR Labeler

on:
  pull_request_target:
    types:
      - opened
      - edited

permissions:
  # Permits `github/issue-labeler` to add a label to a pull request
  pull-requests: write
  contents: read

jobs:
  change-labeling:
    name: Labeling for changes
    runs-on: ubuntu-latest
    if: github.repository == 'web-infra-dev/midscene'
    steps:
      - uses: github/issue-labeler@v3.4
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          configuration-path: .github/pr-labeler.yml
          enable-versioned-regex: 0
          include-title: 1
          sync-labels: 1

