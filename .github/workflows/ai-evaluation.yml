name: AI evaluation
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to checkout'
        required: false
        default: 'main'
        type: string

permissions:
  contents: read

jobs:
  main:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      OPENAI_API_KEY: ${{ secrets.DOUBAO_TOKEN }}
      OPENAI_BASE_URL: ${{ secrets.DOUBAO_BASE_URL }}
      MIDSCENE_MODEL_NAME: 'doubao-1-5-thinking-vision-pro-250428'
      MIDSCENE_USE_DOUBAO_VISION: 1
      CI: 1

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ref: ${{ github.event.inputs.branch || 'main' }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Build project
      run: pnpm run build
    
    - name: Run evaluation
      run: |
        cd packages/evaluation
        pnpm run evaluate:locator
        pnpm run evaluate:planning
        pnpm run evaluate:assertion

    - name: Upload Logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: evaluation-logs
        path: ${{ github.workspace }}/packages/evaluation/tests/__ai_responses__/
        if-no-files-found: ignore