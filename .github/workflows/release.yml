name: Release
on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release Version(minor,patch,preminor,prepatch)'
        required: true
        default: 'prepatch'
      branch:
        description: 'Release Branch(confirm release branch)'
        required: true
        default: 'main'

# Add permissions for creating releases
permissions:
  contents: write
  packages: write

jobs:
  release:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.19.0]
    steps:
    - uses: actions/checkout@v2
      with:
        ref: ${{ github.event.inputs.branch }}
    - name: Pushing to the protected branch 'protected'
      uses: zhoushaw/push-protected@v2
      with:
        token: ${{ secrets.PUSH_TO_PROTECTED_BRANCH }}
        branch: ${{ github.event.inputs.branch }}
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'

    - name: Cache Puppeteer
      uses: actions/cache@v4
      with:
        path: ~/.cache/puppeteer
        key: ${{ runner.os }}-puppeteer-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-puppeteer-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile --ignore-scripts

    - name: Install Puppeteer browser
      run: cd packages/web-integration && npx puppeteer browsers install chrome

    - name: release
      run: node ./scripts/release.js --version=${{ github.event.inputs.version }}
      env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
    
    - name: Get version from core package
      id: get_version
      run: |
        VERSION=$(cat packages/core/package.json | jq -r '.version')
        echo "version=v${VERSION}" >> $GITHUB_OUTPUT
        echo "Core package version: v${VERSION}"
      
    - name: Upload output
      uses: actions/upload-artifact@v4
      with:
        if-no-files-found: error
        name: chrome_extension
        path: ${{ github.workspace }}/apps/chrome-extension/extension_output
        
    - name: Upload Release Assets
      uses: softprops/action-gh-release@v2
      if: ${{ !contains(steps.get_version.outputs.version, 'beta') && !contains(steps.get_version.outputs.version, 'alpha') }}
      with:
        tag_name: ${{ steps.get_version.outputs.version }}
        files: |
          ${{ github.workspace }}/apps/chrome-extension/extension_output/**/*.zip
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
