---
name: LLM Connectivity Issue / 模型连接错误
about: How to solve the LLM connectivity problem
title: "[Connectivity]"
labels: ''
assignees: ''

---

## Read this before open issue

How to choose and config a model: https://midscenejs.com/model-provider.html

Use this project to check the connection: https://github.com/web-infra-dev/midscene-example/tree/main/connectivity-test

## If the error persists, tell us these information

- Where are you using Midscene.js (Chrome extension, yaml with cli, <PERSON><PERSON>peteer,…)

- The version of Midscene.js or Extension

- The error message

- The model name and endpoint (if could be public）

## Security Check

Do NOT include your API key in your issue! Revoke it immediately if it has already been leaked in your issue.
