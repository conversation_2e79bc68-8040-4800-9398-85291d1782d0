@import '../common.less';

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 14px;
}

.prompt-input-wrapper {
  width: 100%;

  /* top operation button area */
  .mode-radio-group-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .mode-radio-group {
      display: flex;
      align-items: center;
      height: 100%;

      .ant-radio-button-wrapper {
        height: 24px;
        padding: 0 8px;
        line-height: 24px;
        border-radius: 11px;
        margin-right: 8px;
        box-shadow: none;
        border: none;
        background-color: #F7F7F7;
        font-size: 12px;

        &:before {
          display: none;
        }

        &:focus-within {
          outline: none;
        }

        &.ant-radio-button-wrapper-checked {
          background-color: #2B83FF;
          border-color: #2B83FF;
          color: white;

          &:hover {
            color: #fff;
          }
        }

        &:hover {
          color: #2B83FF;
        }
      }
    }

    .action-icons {
      display: flex;
      align-items: center;
    }
  }

  .main-side-console-input {
    position: relative;
    margin-top: 10px;

    .main-side-console-input-textarea {
      border-radius: 12px;
      border: 1px solid #F2F4F7;
      background: white;

      @keyframes hue-shift {
        0% {
          filter: hue-rotate(0deg);
        }

        100% {
          filter: hue-rotate(360deg);
        }
      }

      &:focus-within {
        border: 1px solid transparent;
        background:
          linear-gradient(white, white) padding-box,
          linear-gradient(135deg, #4285f4 0%, #0066FF 25%, #7B02C5 50%, #ea4335 75%, #ff7043 100%) border-box;
      }

      padding: 12px 16px;
      transition: background-color 0.2s ease;
      overflow-y: auto;
      white-space: pre-wrap;
      line-height: 21px;

      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
    }

    &.loading .main-side-console-input-textarea {
      border: 1px solid transparent;
      background:
        linear-gradient(white, white) padding-box,
        linear-gradient(135deg, #4285f4 0%, #0066FF 25%, #7B02C5 50%, #ea4335 75%, #ff7043 100%) border-box;
      animation: hue-shift 5s linear infinite;
    }

    .ant-form-item-control-input-content {
      border: 3px solid transparent;
      border-radius: 14px;
      z-index: 999;
    }

    &:focus-within .ant-form-item-control-input-content {
      border-color: rgba(43, 131, 255, 0.16);
    }

    &.disabled {
      .form-controller-wrapper {
        background-color: transparent;
      }
    }
  }

  .ant-input {
    padding-bottom: 40px;
  }

  .form-controller-wrapper {
    position: absolute;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    width: calc(100% - 32px);
    box-sizing: border-box;
    align-items: flex-end;
    gap: 8px;
    background-color: #FFF;
    left: 16px;
    bottom: 0.5px;
    padding: 12px 0;
    line-height: 32px;
    transition: background-color 0.2s ease;
  }

  .settings-wrapper {
    &.settings-wrapper-hover {
      color: @main-text;
    }

    display: flex;
    flex-direction: row;
    gap: 2px;
    color: @weak-text;
    flex-wrap: wrap;
  }
}

.selector-trigger {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;

  .action-icon {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    transition: all 0.2s;

    &:hover {
      color: #2B83FF;
    }
  }
}

.history-modal-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
  border-radius: 12px 12px 0 0;
  overflow: hidden;

  /* top title bar */
  .history-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    line-height: 48px;
    padding: 0 25px;

    .close-button {
      margin-right: -4px;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon {
        font-size: 18px;
        color: #999999;
      }

      &:hover .anticon {
        color: #666666;
      }
    }
  }

  /* search bar */
  .history-search-section {
    padding: 16px 20px;
    background: #ffffff;

    .search-input-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;
      color: rgba(0, 0, 0, 0.25);

      .search-input {
        flex: 1;
        height: 36px;
        border-radius: 16px;
        background: rgba(241, 242, 243, 1);
        border: none;

        .ant-input {
          background: transparent;
          border: none;
          box-shadow: none;
        }

        &:hover,
        &:focus-within {
          border-color: #d9d9d9;
          background: #ffffff;
        }
      }

      .clear-button {
        color: #1890ff;
        padding: 0;
        height: auto;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  /* history content */
  .history-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 25px 25px 25px;

    .history-group {
      margin-bottom: 10px;

      .history-group-title {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        height: 40px;
        line-height: 40px;
        font-weight: 400;
      }

      .history-item {
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &:hover {
          background: rgba(242, 244, 247, 1);
          padding: 0 8px;
          margin: 0 -8px;
        }
      }
    }

    .no-results {
      text-align: center;
      padding: 40px 20px;
      color: #999999;
    }
  }
}

.ant-modal-wrap .ant-modal-content {
  animation: slideUpFromBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

@keyframes slideUpFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Playground result area styles */
.result-wrapper {
  display: flex;
  height: 100%;
  justify-content: center;
  margin: 4px 0;

  .loading-container {
    text-align: center;
  }

  pre {
    margin: 4px 0;
    margin: 0;
    white-space: pre-wrap;
    text-wrap: unset;
    word-wrap: break-word;
    overflow-wrap: break-word;
    background: #F2F4F7;
    border-radius: 8px;
    padding: 14px;
    overflow: scroll;
  }
}