import type {
  ExecutionDump,
  ExecutionTaskInsightLocate,
  InsightDump,
} from '@midscene/core';
import dayjs from 'dayjs';

export function insightDumpToExecutionDump(
  insightDump: InsightDump | InsightDump[],
): ExecutionDump {
  const insightToTask = (
    insightDump: InsightDump,
  ): ExecutionTaskInsightLocate => {
    const task: ExecutionTaskInsightLocate = {
      type: 'Insight',
      subType: insightDump.type === 'locate' ? 'Locate' : 'Query',
      status: insightDump.error ? 'failed' : 'finished',
      locate: null,
      param: {
        ...(insightDump.userQuery.element
          ? { query: insightDump.userQuery }
          : {}),
        ...(insightDump.userQuery.dataDemand
          ? { dataDemand: insightDump.userQuery.dataDemand }
          : {}),
        insight: {} as any,
      } as any,
      log: {
        dump: insightDump,
      },
      timing: {
        end: insightDump.logTime,
        cost: insightDump.taskInfo?.durationMs,
        start: insightDump.logTime - insightDump.taskInfo?.durationMs,
      },
      executor: () => {},
    };
    return task;
  };

  if (!Array.isArray(insightDump)) {
    const result: ExecutionDump = {
      sdkVersion: insightDump.sdkVersion,
      logTime: insightDump.logTime,
      model_name: insightDump.model_name,
      name: 'Insight',
      tasks: [insightToTask(insightDump)],
    };
    return result;
  }
  const result: ExecutionDump = {
    sdkVersion: insightDump[0].sdkVersion,
    logTime: insightDump[0].logTime,
    model_name: insightDump[0].model_name,
    name: 'Insight',
    tasks: insightDump.map(insightToTask),
  };
  return result;
}

export function timeStr(timestamp?: number) {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-';
}

export function filterBase64Value(input: string) {
  return input.replace(/data:image\/[^"]+"/g, 'data:image..."');
}

export const mousePointer =
  'data:image/png;base64,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**************************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';

export const mouseLoading =
  'data:image/png;base64,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';
