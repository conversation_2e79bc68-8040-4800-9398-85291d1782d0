{"compilerOptions": {"baseUrl": ".", "sourceMap": true, "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["DOM", "ESNext"], "moduleResolution": "node", "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "rootDir": "./src", "skipLibCheck": true, "strict": true, "module": "ESNext", "target": "es2018"}, "exclude": ["**/node_modules"], "include": ["src", "report"]}