{"compilerOptions": {"baseUrl": ".", "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["DOM", "ESNext"], "moduleResolution": "node", "paths": {"@/*": ["./src/*"]}, "target": "ESNext", "resolveJsonModule": true, "rootDir": "./", "skipLibCheck": true, "strict": true, "module": "ESNext"}, "exclude": ["node_modules"], "include": ["src", "tests", "./playwright.config.ts", "./vitest.config"]}