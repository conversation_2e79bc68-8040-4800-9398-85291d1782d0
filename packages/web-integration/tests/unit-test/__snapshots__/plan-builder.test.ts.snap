// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`build plans > hover 1`] = `
[
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "prompt": "OK button",
    },
    "thought": "",
    "type": "Locate",
  },
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": null,
    "thought": "",
    "type": "Hover",
  },
]
`;

exports[`build plans > input 1`] = `
[
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "prompt": "OK button",
    },
    "thought": "",
    "type": "Locate",
  },
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "value": "OK",
    },
    "thought": "",
    "type": "Input",
  },
]
`;

exports[`build plans > keyboardPress 1`] = `
[
  {
    "locate": undefined,
    "param": {
      "value": "OK",
    },
    "thought": "",
    "type": "KeyboardPress",
  },
]
`;

exports[`build plans > rightClick 1`] = `
[
  {
    "locate": {
      "prompt": "context menu target",
    },
    "param": {
      "prompt": "context menu target",
    },
    "thought": "",
    "type": "Locate",
  },
  {
    "locate": {
      "prompt": "context menu target",
    },
    "param": null,
    "thought": "",
    "type": "RightClick",
  },
]
`;

exports[`build plans > scroll 1`] = `
[
  {
    "locate": undefined,
    "param": {
      "direction": "down",
      "distance": 100,
      "scrollType": "once",
    },
    "thought": "",
    "type": "Scroll",
  },
]
`;

exports[`build plans > scroll with locate 1`] = `
[
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "prompt": "OK button",
    },
    "thought": "",
    "type": "Locate",
  },
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "direction": "right",
      "scrollType": "untilRight",
    },
    "thought": "",
    "type": "Scroll",
  },
]
`;

exports[`build plans > sleep 1`] = `
[
  {
    "locate": null,
    "param": {
      "timeMs": 1000,
    },
    "thought": "",
    "type": "Sleep",
  },
]
`;

exports[`build plans > tap 1`] = `
[
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": {
      "prompt": "OK button",
    },
    "thought": "",
    "type": "Locate",
  },
  {
    "locate": {
      "prompt": "OK button",
    },
    "param": null,
    "thought": "",
    "type": "Tap",
  },
]
`;
