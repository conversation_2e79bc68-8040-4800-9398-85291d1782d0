# login to sauce demo, extract the items info into a json file, and assert the price of 'Sauce Labs Fleece Jacket'

web:
  url: https://www.saucedemo.com/

tasks:
  - name: login
    flow:
      - aiAction: type 'standard_user' in user name input, type 'secret_sauce' in password, click 'Login'

  - name: extract items info
    flow:
      - aiQuery: >
          {name: string, price: number, actionBtnName: string, imageUrl: string}[], return item name, price and the action button name on the lower right corner of each item, and the image url of each item (like 'Remove')
        name: items
        domIncluded: true
      - aiAssert: The price of 'Sauce Labs Fleece Jacket' is 49.99

  - name: run javascript code
    flow:
      - javascript: >
          document.title
        name: page-title
