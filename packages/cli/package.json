{"name": "@midscene/cli", "description": "An AI-powered automation SDK can control the page, perform assertions, and extract data in JSON format using natural language. See https://midscenejs.com/ for details.", "version": "0.25.3", "repository": "https://github.com/web-infra-dev/midscene", "homepage": "https://midscenejs.com/", "jsnext:source": "./src/index.ts", "main": "./dist/lib/index.js", "bin": {"midscene": "./bin/midscene"}, "files": ["dist", "README.md", "bin"], "scripts": {"dev": "npm run build:watch", "build": "modern build", "build:watch": "modern build -w --no-clear", "new": "modern new", "upgrade": "modern upgrade", "test": "vitest --run", "test:ai": "AITEST=true vitest --run", "test:ai:temp": "AITEST=true BRIDGE_MODE=true vitest tests/bridge.test.ts"}, "dependencies": {"@midscene/android": "workspace:*", "@midscene/core": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/web": "workspace:*", "http-server": "14.1.1", "lodash.merge": "4.6.2", "puppeteer": "24.2.0"}, "devDependencies": {"@modern-js/module-tools": "2.60.6", "@types/js-yaml": "4.0.9", "@types/lodash.merge": "4.6.9", "@types/minimist": "1.2.5", "@types/node": "^18.0.0", "@types/yargs": "17.0.32", "chalk": "4.1.2", "cli-spinners": "3.2.0", "dotenv": "16.4.5", "execa": "9.3.0", "glob": "11.0.0", "js-yaml": "4.1.0", "p-limit": "6.2.0", "restore-cursor": "5.1.0", "typescript": "^5.8.3", "vitest": "3.0.5", "yargs": "17.7.2"}, "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "license": "MIT"}