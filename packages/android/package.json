{"name": "@midscene/android", "version": "0.25.3", "description": "Android automation library for Midscene", "keywords": ["Android UI automation", "Android AI testing", "Android automation library", "Android automation tool", "Android use"], "main": "./dist/lib/index.js", "types": "./dist/types/index.d.ts", "files": ["bin", "dist", "README.md"], "exports": {".": {"types": "./dist/types/index.d.ts", "default": "./dist/lib/index.js"}, "./package.json": "./package.json"}, "scripts": {"dev": "npm run build:watch", "build": "modern build -c ./modern.config.ts", "build:watch": "modern build -w -c ./modern.config.ts --no-clear", "test": "vitest --run", "test:u": "vitest --run -u", "test:ai": "AI_TEST_TYPE=android npm run test", "test:ai:cache": "MIDSCENE_CACHE=true AI_TEST_TYPE=android npm run test"}, "dependencies": {"@midscene/core": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/web": "workspace:*", "appium-adb": "12.12.1"}, "devDependencies": {"@modern-js/module-tools": "2.60.6", "@types/node": "^18.0.0", "dotenv": "16.4.5", "typescript": "^5.8.3", "vitest": "3.0.5"}, "license": "MIT"}