{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"build": ["default", "{projectRoot}/tsconfig.json", "{projectRoot}/package.json", "{projectRoot}/modern.config.*", "{projectRoot}/scripts/**/*", "!{projectRoot}/**/*.{md,mdx}", "!{projectRoot}/vitest.config.ts", "!{projectRoot}/**/?(*.)+(spec|test).ts"]}, "targetDefaults": {"dev": {"dependsOn": ["^build"]}, "build": {"dependsOn": ["^build"], "cache": true, "inputs": ["build", "^build", "{workspaceRoot}/package.json"], "outputs": ["{projectRoot}/dist"]}, "build:watch": {"dependsOn": ["^build"]}, "test": {"cache": false}, "e2e": {"dependsOn": ["^build"]}, "e2e:ui": {"dependsOn": ["^build"]}}, "defaultBase": "main"}