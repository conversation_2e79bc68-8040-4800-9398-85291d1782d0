{"name": "midscene", "private": true, "version": "0.25.3", "scripts": {"dev": "nx run-many --target=build:watch --exclude=android-playground,chrome-extension,@midscene/report,doc --verbose --parallel=8", "build": "nx run-many --target=build --exclude=doc --verbose", "build:skip-cache": "nx run-many --target=build --exclude=doc --verbose --skip-nx-cache", "test": "nx run-many --target=test --projects=@midscene/core,@midscene/shared,@midscene/visualizer,@midscene/web,@midscene/cli,@midscene/android --verbose", "test:ai": "nx run-many --target=test:ai --projects=@midscene/core,@midscene/web,@midscene/cli --verbose", "e2e": "nx run @midscene/web:e2e --verbose --exclude-task-dependencies", "e2e:cache": "nx run @midscene/web:e2e:cache --verbose  --exclude-task-dependencies", "e2e:report": "nx run @midscene/web:e2e:report --verbose  --exclude-task-dependencies", "e2e:visualizer": "nx run @midscene/visualizer:e2e --verbose  --exclude-task-dependencies", "test:ai:all": "npm run e2e && npm run e2e:cache && npm run e2e:report && npm run test:ai && npm run e2e:visualizer", "prepare": "simple-git-hooks && husky && pnpm run build", "check-dependency-version": "check-dependency-version-consistency .", "lint": "npx biome check . --diagnostic-level=info --no-errors-on-unmatched --fix", "format:ci": "pretty-quick --since HEAD~1", "format": "pretty-quick --staged", "commit": "cz", "check-spell": "npx cspell"}, "simple-git-hooks": {"pre-commit": "npx nano-staged"}, "nano-staged": {"*.{md,mdx,json,css,less,scss}": "npx biome check . --diagnostic-level=info --no-errors-on-unmatched --fix --verbose", "*.{js,jsx,ts,tsx,mjs,cjs,json}": ["npx biome check . --diagnostic-level=info --no-errors-on-unmatched --fix --verbose"], "package.json": "pnpm run check-dependency-version"}, "keywords": [], "author": "", "license": "MIT", "engines": {"pnpm": ">=9.3.0", "node": ">=18.19.0"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@changesets/cli": "2.24.1", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@esm2cjs/execa": "6.1.1-cjs.1", "@jsdevtools/version-bump-prompt": "6.1.0", "chalk": "4.1.2", "check-dependency-version-consistency": "^4.1.1", "commitizen": "4.2.5", "cspell-ban-words": "^0.0.3", "dayjs": "^1.11.11", "husky": "9.1.7", "minimist": "1.2.5", "nano-staged": "^0.8.0", "nx": "21.1.2", "prettier": "^3.5.3", "pretty-quick": "3.1.3", "semver": "7.5.2", "simple-git-hooks": "^2.13.0"}}